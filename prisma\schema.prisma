generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for authentication
model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  accounts      Account[]
  sessions      Session[]
  reviews       Review[]
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Category model
model Category {
  id          Int       @id @default(autoincrement())
  title       String
  slug        String    @unique
  image       String?
  description String?
  products    Product[]
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

// Product model
model Product {
  id                     String                  @id @default(cuid())
  title                  String
  shortDescription       String
  description            String?
  price                  Decimal                 @db.Decimal(10, 2)
  discountedPrice        Decimal?                @db.Decimal(10, 2)
  slug                   String                  @unique
  quantity               Int
  sku                    String?
  body                   String?
  tags                   String[]
  offers                 String[]
  categoryId             Int?
  category               Category?               @relation(fields: [categoryId], references: [id])
  productVariants        ProductVariant[]
  reviews                Review[]
  additionalInformation  AdditionalInformation[]
  customAttributes       CustomAttribute[]
  heroBanners            HeroBanner[]
  countdowns             Countdown[]
  createdAt              DateTime                @default(now())
  updatedAt              DateTime                @updatedAt
}

// Product variant model
model ProductVariant {
  id        String  @id @default(cuid())
  color     String
  image     String
  size      String
  isDefault Boolean @default(false)
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)
}

// Additional information model
model AdditionalInformation {
  id          String  @id @default(cuid())
  name        String
  description String
  productId   String
  product     Product @relation(fields: [productId], references: [id], onDelete: Cascade)
}

// Custom attribute model
model CustomAttribute {
  id              String            @id @default(cuid())
  attributeName   String
  attributeValues AttributeValue[]
  productId       String
  product         Product           @relation(fields: [productId], references: [id], onDelete: Cascade)
}

// Attribute value model
model AttributeValue {
  id                String          @id @default(cuid())
  title             String
  customAttributeId String
  customAttribute   CustomAttribute @relation(fields: [customAttributeId], references: [id], onDelete: Cascade)
}

// Review model
model Review {
  id          String   @id @default(cuid())
  rating      Int
  comment     String?
  isApproved  Boolean  @default(false)
  productSlug String
  userId      String?
  user        User?    @relation(fields: [userId], references: [id])
  productId   String
  product     Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// Hero banner model
model HeroBanner {
  id          String   @id @default(cuid())
  title       String?
  subtitle    String?
  image       String
  buttonText  String?
  buttonLink  String?
  productId   String?
  product     Product? @relation(fields: [productId], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// Countdown model
model Countdown {
  id        String   @id @default(cuid())
  title     String
  endDate   DateTime
  productId String?
  product   Product? @relation(fields: [productId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Header settings model
model HeaderSetting {
  id          String   @id @default(cuid())
  headerLogo  String?
  emailLogo   String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}
