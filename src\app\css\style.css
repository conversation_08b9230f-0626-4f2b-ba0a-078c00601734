@import url("https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap");

@import "tailwindcss";
@plugin "@tailwindcss/typography";
@plugin "@tailwindcss/forms";

@custom-variant dark (&:is(.dark *));

@theme {
  --font-*: initial;
  --font-body: "DM Sans", sans-serif;

  --color-*: initial;
  --color-current: currentColor;
  --color-transparent: transparent;
  --color-white: #ffffff;
  --color-body: #6c6f93;

  --color-meta-2: #495270;
  --color-meta-3: #606882;
  --color-meta-4: #8d93a5;
  --color-meta-5: #bbbec9;
  --color-meta: #f7f9fc;

  --color-dark-2: #495270;
  --color-dark-3: #606882;
  --color-dark-4: #8d93a5;
  --color-dark-5: #bbbec9;
  --color-dark: #1c274c;
  --color-darkLight: #3d486b;

  --color-gray-1: #f9fafb;
  --color-gray-2: #f3f4f6;
  --color-gray-3: #e5e7eb;
  --color-gray-4: #d1d5db;
  --color-gray-5: #9ca3af;
  --color-gray-6: #6b7280;
  --color-gray-7: #374151;
  --color-gray: #f3f5f6;

  --color-blue: #3c50e0;
  --color-blue-dark: #1c3fb7;
  --color-blue-light: #5475e5;
  --color-blue-light-2: #8099ec;
  --color-blue-light-3: #adbcf2;
  --color-blue-light-4: #c3cef6;
  --color-blue-light-5: #e1e8ff;

  --color-red-50: #fef3f2;
  --color-red-500: #d92d20;
  --color-red-600: #dc2626;

  --color-red: #f23030;
  --color-red-dark: #e10e0e;
  --color-red-light: #f56060;
  --color-red-light-2: #f89090;
  --color-red-light-3: #fbc0c0;
  --color-red-light-4: #fdd8d8;
  --color-red-light-5: #feebeb;
  --color-red-light-6: #fef3f3;

  --color-success-50: #ecfdf3;
  --color-success-500: #039855;
  --color-green: #22ad5c;
  --color-green-dark: #1a8245;
  --color-green-light: #2cd673;
  --color-green-light-2: #57de8f;
  --color-green-light-3: #82e6ac;
  --color-green-light-4: #acefc8;
  --color-green-light-5: #c2f3d6;
  --color-green-light-6: #daf8e6;

  --color-yellow-50: #fffaeb;
  --color-yellow-500: #dc6803;
  --color-yellow: #fbbf24;
  --color-yellow-dark: #f59e0b;
  --color-yellow-dark-2: #d97706;
  --color-yellow-light: #fcd34d;
  --color-yellow-light-1: #fde68a;
  --color-yellow-light-2: #fef3c7;
  --color-yellow-light-4: #fffbeb;

  --color-teal: #02aaa4;
  --color-teal-dark: #06a09b;

  --color-orange: #f27430;
  --color-orange-dark: #e1580e;

  --breakpoint-*: initial;
  --breakpoint-xsm: 375px;
  --breakpoint-lsm: 425px;
  --breakpoint-3xl: 2000px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  --text-2xs: 10px;
  --text-custom-xs: 12px;
  --text-custom-sm: 14px;
  --text-custom-lg: 18px;
  --text-custom-xl: 20px;
  --text-custom-2xl: 24px;
  --text-custom-4xl: 36px;
  --text-custom-1: 22px;
  --text-custom-2: 32px;
  --text-custom-3: 35px;
  --text-heading-1: 60px;
  --text-heading-2: 48px;
  --text-heading-3: 40px;
  --text-heading-4: 30px;
  --text-heading-5: 28px;
  --text-heading-6: 24px;

  --container-30: 7.5rem;
  --container-40: 10rem;
  --container-50: 12.5rem;

  --z-index-1: 1;
  --z-index-99: 99;
  --z-index-999: 999;
  --z-index-9999: 9999;
  --z-index-99999: 99999;
  --z-index-999999: 999999;

  --shadow-1: 0px 1px 2px 0px rgba(166, 175, 195, 0.25);
  --shadow-2: 0px 6px 24px 0px rgba(235, 238, 251, 0.4),
    0px 2px 4px 0px rgba(148, 163, 184, 0.05);
  --shadow-3: 0px 2px 16px 0px rgba(13, 10, 44, 0.12);
  --shadow-testimonial: 0px 0px 4px 0px rgba(148, 163, 184, 0.1),
    0px 6px 12px 0px rgba(224, 227, 238, 0.45);
  --shadow-breadcrumb: 0px 1px 0px 0px #e5e7eb, 0px -1px 0px 0px #e5e7eb;
  --shadow-range: 0px 0px 1px 0px rgba(33, 37, 41, 0.08),
    0px 2px 2px 0px rgba(33, 37, 41, 0.06);
  --shadow-filter: 0px 1px 0px 0px #e5e7eb;
  --shadow-list: 1px 0px 0px 0px #e5e7eb;
  --shadow-input: inset 0 0 0 2px #3c50e0;
}

@layer base {
  button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }
}

@utility container {
  margin-inline: auto;
  padding-inline: 1rem;
  @media (width >= theme(--breakpoint-sm)) {
    padding-inline: 2rem;
  }
  @media (width >= theme(--breakpoint-xl)) {
    padding-inline: 0;
  }
}

@utility text-2xs {
  font-size: theme(--text-2xs);
  line-height: 17px;
}

@utility text-custom-xs {
  font-size: theme(--text-custom-xs);
  line-height: 20px;
}

@utility text-custom-sm {
  font-size: theme(--text-custom-sm);
  line-height: 22px;
}

@utility text-custom-lg {
  font-size: theme(--text-custom-lg);
  line-height: 24px;
}

@utility text-custom-xl {
  font-size: theme(--text-custom-xl);
  line-height: 24px;
}

@utility text-custom-2xl {
  font-size: theme(--text-custom-2xl);
  line-height: 34px;
}

@utility text-custom-4xl {
  font-size: theme(--text-custom-4xl);
  line-height: 48px;
}

@utility text-custom-1 {
  font-size: theme(--text-custom-1);
  line-height: 30px;
}

@utility text-custom-2 {
  font-size: theme(--text-custom-2);
  line-height: 38px;
}

@utility text-custom-3 {
  font-size: theme(--text-custom-3);
  line-height: 45px;
}

@utility text-heading-1 {
  font-size: theme(--text-heading-1);
  line-height: 72px;
}

@utility text-heading-2 {
  font-size: theme(--text-heading-2);
  line-height: 64px;
}

@utility text-heading-3 {
  font-size: theme(--text-heading-3);
  line-height: 48px;
}

@utility text-heading-4 {
  font-size: theme(--text-heading-4);
  line-height: 38px;
}

@utility text-heading-5 {
  font-size: theme(--text-heading-5);
  line-height: 40px;
}

@utility text-heading-6 {
  font-size: theme(--text-heading-6);
  line-height: 30px;
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

@utility dropdown {
  @apply flex-col gap-0 min-w-max xl:w-[193px] mt-2 lg:mt-0 bg-white shadow-2 ease-in duration-200 py-2.5 rounded-md border border-gray-3 left-0 hidden
    xl:translate-y-10 xl:opacity-0 xl:invisible xl:absolute xl:flex
    xl:group-hover:opacity-100 xl:group-hover:visible;
}

@utility no-scrollbar {
  /* Chrome, Safari and Opera */
  &::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

@layer base {
  html {
    @apply scroll-smooth;
  }

  body {
    @apply relative text-base font-normal font-body text-dark-3 z-1;
  }
}

/* third-party libraries CSS */

/* clears the ‘X’ from Internet Explorer */
input[type="search"]::-ms-clear {
  display: none;
  width: 0;
  height: 0;
}
input[type="search"]::-ms-reveal {
  display: none;
  width: 0;
  height: 0;
}
/* clears the ‘X’ from Chrome */
input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration {
  display: none;
}
.custom-search {
  -webkit-border-radius: 0;
  -webkit-border-top-right-radius: 5px;
  -webkit-border-bottom-right-radius: 5px;
}

/* The container must be positioned relative: */
.custom-select select {
  display: none; /*hide original SELECT element: */
  border-radius: 8px;
}

.select-selected {
  @apply bg-gray-1 rounded-l-[5px] border border-gray-3 border-r-0! cursor-pointer text-dark text-custom-sm py-[11px] pl-3.5 pr-8 relative;
}

/* Style the arrow inside the select element: */
.select-selected:after {
  content: url("data:image/svg+xml,%3Csvg width='10' height='6' viewBox='0 0 10 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.833313 0.916687L4.99998 5.08335L9.16665 0.916687' stroke='%231C274C' strokeWidth='1.5' strokeLinecap='round' strokeLinejoin='round'/%3E%3C/svg%3E%0A");
  @apply absolute top-1/2 -translate-y-1/2 right-4.5 ease-out duration-200;
}

/* Point the arrow upwards when the select box is open (active): */
.select-selected.select-arrow-active:after {
  @apply rotate-180 mt-0.5;
}

/* style the items (options), including the selected item: */

/* Style items (options): */
.select-items {
  @apply absolute top-full left-0 right-0 z-99 shadow-2 border border-gray-3 bg-white rounded-md py-2.5 mt-1.5;
}

/* Hide the items when the select box is closed: */
.select-hide {
  display: none;
}

.same-as-selected {
  @apply bg-gray-1 text-dark;
}

.custom-select-2 .select-selected {
  @apply bg-white rounded-md border-r! text-dark font-medium text-sm leading-[22px] py-1.5 pl-3 pr-9;
}
.custom-select-2 .select-selected:after {
  content: url("data:image/svg+xml,%3Csvg width='14' height='8' viewBox='0 0 14 8' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.75 0.875001L7 7.125L13.25 0.875' stroke='%238D93A5' strokeWidth='1.5' strokeLinecap='round' strokeLinejoin='round'/%3E%3C/svg%3E%0A");
  @apply right-3.5;
}

.custom-select-common .select-selected {
  @apply bg-gray-1 rounded-md border-r! text-dark-4 py-3 pl-5 pr-9 duration-200 focus:border-transparent focus:shadow-input focus:ring-2 focus:ring-blue/20;
}
.custom-select-common .select-selected:after {
  content: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 16 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M2.41469 5.03569L2.41467 5.03571L2.41749 5.03846L7.76749 10.2635L8.0015 10.492L8.23442 10.2623L13.5844 4.98735L13.5844 4.98735L13.5861 4.98569C13.6809 4.89086 13.8199 4.89087 13.9147 4.98569C14.0092 5.08024 14.0095 5.21864 13.9155 5.31345C13.9152 5.31373 13.915 5.31401 13.9147 5.31429L8.16676 10.9622L8.16676 10.9622L8.16469 10.9643C8.06838 11.0606 8.02352 11.0667 8.00039 11.0667C7.94147 11.0667 7.89042 11.0522 7.82064 10.9991L2.08526 5.36345C1.99127 5.26865 1.99154 5.13024 2.08609 5.03569C2.18092 4.94086 2.31986 4.94086 2.41469 5.03569Z' fill='%238D93A5' stroke='%238D93A5' strokeWidth='0.666667'/%3E%3C/svg%3E%0A");
  @apply block mt-1 right-4;
}
.custom-select-common .select-selected.select-arrow-active:after {
  @apply -mt-0.5;
}

.hero-carousel .swiper-pagination-bullet {
  @apply h-1 w-4 rounded-[11px] bg-dark-5 opacity-100;
}

.hero-carousel .swiper-pagination-bullet-active {
  @apply w-5.5 bg-blue;
}

.hero-carousel .swiper-pagination {
  @apply xl:bottom-5!;
}

.common-carousel .swiper-button-next:after,
.common-carousel .swiper-button-prev:after {
  @apply hidden;
}

.common-carousel .swiper-button-next,
.common-carousel .swiper-button-prev {
  @apply static! h-9 w-9 rounded-lg bg-white text-dark border border-gray-3 m-0 ease-out duration-200 hover:bg-blue hover:text-white hover:border-blue;
}

.common-carousel .swiper-button-next svg,
.common-carousel .swiper-button-prev svg {
  @apply w-auto h-auto;
}

.img-zoom-container img {
  transition: 0.8s;
}
.img-zoom-container img:hover {
  transform: scale(2) rotate(0deg);
  transition: 0.8s;
}

.priceSlide .noUi-target {
  @apply mb-5 bg-transparent border-none shadow-none mt-9;
}
.priceSlide .noUi-connects {
  @apply h-1 rounded-full bg-gray-3;
}
.priceSlide .noUi-connect {
  @apply h-1 rounded-full bg-blue;
}
.priceSlide .noUi-horizontal .noUi-handle {
  @apply bg-white border rounded-full -top-3 h-7 w-7 border-gray-4 shadow-range before:block before:absolute before:left-1/2 before:top-1/2 before:-translate-x-1/2 before:-translate-y-1/2 before:w-4 before:h-4 before:rounded-full before:bg-blue;
}
.priceSlide .noUi-tooltip {
  @apply p-0 bg-transparent border-none text-custom-sm text-dark-4;
}
.noUi-horizontal .noUi-handle:after,
.noUi-horizontal .noUi-handle:before {
  @apply hidden;
}

.range-slider {
  @apply w-full h-[4px]! rounded-md bg-blue relative mb-3;
}

.slider .thumb {
  @apply w-6 h-6 bg-blue-dark rounded-full absolute -top-2 border-[5px] border-blue-light-4;
}

.range-slider__thumb {
  @apply bg-white! border-[1px] border-blue-light-4 flex justify-center items-center w-7! h-7!;
}

.range-slider__thumb::after {
  content: "";
  width: 16px;
  height: 16px;
  display: block;
  border-radius: 100%;
  position: absolute;
  @apply bg-blue;
}

.range-slider__range {
  @apply bg-blue!;
}

.slider-track {
  @apply bg-gray-3! h-1!;
}

.slider-range {
  @apply bg-blue!;
}

.blog-details p {
  @apply mb-6;
}

.blog-details ul {
  @apply pl-6 list-disc;
}

.blog-details blockquote {
  @apply rounded-xl bg-white pt-7.5 pb-6 px-4 sm:px-7.5 my-7.5 italic text-dark text-center;
}

.blog-details h2 {
  @apply font-medium text-dark text-lg xl:text-[30px] xl:leading-[34px] mb-6;
}

.blog-details h3 {
  @apply font-medium text-dark text-lg xl:text-[26px] xl:leading-[34px] mb-6;
}

.blog-details h4 {
  @apply font-medium text-dark text-lg xl:text-[20px] xl:leading-[34px] mb-6;
}

.blog-details h5 {
  @apply font-medium text-dark text-lg xl:text-[18px] xl:leading-[34px] mb-6;
}

.testimonial-swiper .swiper-wrapper {
  @apply py-5!;
}

@utility custom-scrollbar {
  &::-webkit-scrollbar {
    @apply size-1.5;
  }

  &::-webkit-scrollbar-track {
    @apply rounded-full;
  }

  &::-webkit-scrollbar-thumb {
    @apply rounded-full bg-gray-2 dark:bg-gray-7;
  }
}

/* Pagination */
/* .pagination ul {
  display: flex;
  align-items: center;
  gap: 10px;
}
.pagination ul li {
  @apply border-gray-1;
  text-align: center;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  @apply bg-white;
}
.pagination ul li:hover {
  @apply bg-blue/10 text-blue;
}
.pagination ul li a {
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  width: 40px;
  height: 40px;
}
.pagination.bg-2 ul li {
  @apply bg-gray-2;
  color: #1c274c;
}
.pagination ul li.current {
  @apply bg-blue;
  color: #fff;
}
.pagination ul li.disabled {
  display: none;
} */

/* react quill */
.ql-toolbar.ql-snow + .ql-container.ql-snow {
  height: 150px;
  border: 1px solid #e5e7eb;
  border-radius: 0 0 6px 6px;
}
.ql-toolbar.ql-snow {
  border: 1px solid #e5e7eb !important;
}
.ql-toolbar.ql-snow {
  border-radius: 6px 6px 0 0;
}

.prose p {
  font-size: 16px;
  font-weight: 400;
}
.prose ol li,
.prose ul li {
  font-size: 16px;
  line-height: 1.5;
}
