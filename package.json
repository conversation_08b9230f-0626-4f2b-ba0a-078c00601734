{"name": "cozycommerce", "version": "1.0.2", "private": true, "scripts": {"dev": "next dev", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "algolia:reindex": "node src/algolia/reindex.ts", "import-demo-data": "node scripts/import-demo-data.js"}, "dependencies": {"@auth/prisma-adapter": "^1.0.10", "@next/third-parties": "^15.3.2", "@prisma/client": "^6.5.0", "@radix-ui/react-slider": "^1.2.0", "@react-email/components": "^0.0.38", "@react-email/render": "^1.1.0", "@reduxjs/toolkit": "^2.2.2", "@stripe/react-stripe-js": "^3.5.0", "@stripe/stripe-js": "^3.1.0", "@tippyjs/react": "^4.2.6", "algoliasearch": "^4.23.3", "axios": "^1.6.8", "bcrypt": "^5.1.1", "chart.js": "^4.4.8", "cheerio": "^1.0.0-rc.12", "cloudinary": "^2.5.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "next": "^15.2.4", "next-auth": "^4.24.11", "next-share": "^0.27.0", "nextjs-toploader": "^1.6.12", "nodemailer": "^6.9.13", "quill": "^2.0.3", "rate-limiter-flexible": "^7.0.0", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.4.1", "react-instantsearch": "^7.7.1", "react-paginate": "^8.3.0", "react-quilljs": "^2.0.5", "react-redux": "^9.1.0", "react-tooltip": "^5.28.1", "stripe": "^14.8.0", "styled-components": "^6.1.15", "sweetalert2": "^11.17.2", "swiper": "^11.0.7", "tailwind-merge": "^3.0.1", "use-shopping-cart": "^3.1.8"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.0.0", "@tailwindcss/typography": "^0.5.16", "@types/bcrypt": "^5.0.2", "@types/node": "^20.11.30", "@types/nodemailer": "^6.4.14", "@types/quill": "^2.0.14", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-instantsearch-dom": "^6.12.7", "@types/react-slider": "^1.3.2", "eslint": "^8.57.0", "eslint-config-next": "15.1.7", "postcss": "^8.4.31", "prisma": "^6.5.0", "tailwindcss": "^4.0.0", "ts-node": "^10.9.2", "typescript": "^5.4.3"}}