type IconProps = React.SVGProps<SVGSVGElement>;

export function CircleCheckIcon(props: IconProps) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="currentColor"
      {...props}
    >
      <g clipPath="url(#clip0_375_9221)">
        <path d="M10 0.5625C4.78125 0.5625 0.5625 4.78125 0.5625 10C0.5625 15.2188 4.78125 19.4688 10 19.4688C15.2188 19.4688 19.4688 15.2188 19.4688 10C19.4688 4.78125 15.2188 0.5625 10 0.5625ZM10 18.0625C5.5625 18.0625 1.96875 14.4375 1.96875 10C1.96875 5.5625 5.5625 1.96875 10 1.96875C14.4375 1.96875 18.0625 5.59375 18.0625 10.0312C18.0625 14.4375 14.4375 18.0625 10 18.0625Z" />
        <path d="M12.6875 7.09374L8.9688 10.7187L7.2813 9.06249C7.00005 8.78124 6.56255 8.81249 6.2813 9.06249C6.00005 9.34374 6.0313 9.78124 6.2813 10.0625L8.2813 12C8.4688 12.1875 8.7188 12.2812 8.9688 12.2812C9.2188 12.2812 9.4688 12.1875 9.6563 12L13.6875 8.12499C13.9688 7.84374 13.9688 7.40624 13.6875 7.12499C13.4063 6.84374 12.9688 6.84374 12.6875 7.09374Z" />
      </g>
      <defs>
        <clipPath id="clip0_375_9221">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function HeartFolderIcon(props: IconProps) {
  return (
    <svg {...props} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" transform="rotate(0 0 0)">
      <path d="M2 6C2 4.75736 3.00736 3.75 4.25 3.75H8.5C9.2082 3.75 9.87508 4.08344 10.3 4.65L11.65 6.45C11.7916 6.63885 12.0139 6.75 12.25 6.75H19.75C20.9926 6.75 22 7.75736 22 9V12.338C21.5725 11.8027 21.0667 11.3327 20.5 10.9453V9C20.5 8.58579 20.1642 8.25 19.75 8.25H12.25C11.5418 8.25 10.8749 7.91656 10.45 7.35L9.1 5.55C8.95836 5.36115 8.73607 5.25 8.5 5.25H4.25C3.83579 5.25 3.5 5.58579 3.5 6V18C3.5 18.4142 3.83579 18.75 4.25 18.75H10.0847C10.2572 19.2813 10.4926 19.7842 10.7822 20.25H4.25C3.00736 20.25 2 19.2426 2 18V6Z" fill="currentColor" />
      <path fillRule="evenodd" clipRule="evenodd" d="M16.4849 13.7069C15.3314 12.5534 13.4612 12.5534 12.3077 13.7069C11.1543 14.8604 11.1543 16.7305 12.3077 17.884L15.3854 20.9617C16.0685 21.6448 17.1758 21.6452 17.8594 20.9626L20.9419 17.8844C22.0954 16.7309 22.0957 14.8604 20.9423 13.7069C19.7888 12.5535 17.9186 12.5535 16.7652 13.7069L16.625 13.8471L16.4849 13.7069ZM13.3684 14.7676C13.9361 14.1999 14.8565 14.1999 15.4242 14.7676L16.0947 15.4381C16.2353 15.5787 16.4261 15.6577 16.625 15.6577C16.8239 15.6577 17.0147 15.5787 17.1553 15.4381L17.8258 14.7676C18.3935 14.1999 19.3139 14.1999 19.8816 14.7676C20.4492 15.3352 20.4493 16.2555 19.8818 16.8232L16.7995 19.9012C16.7018 19.9987 16.5436 19.9986 16.4461 19.901L13.3684 16.8234C12.8007 16.2557 12.8007 15.3353 13.3684 14.7676Z" fill="currentColor" />
    </svg>

  );
}

export function CircleXIcon(props: IconProps) {
  return (
    <svg
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="currentColor"
      {...props}
    >
      <path
        d="M9.19509 8.22222C8.92661 7.95374 8.49131 7.95374 8.22282 8.22222C7.95433 8.49071 7.95433 8.92601 8.22282 9.1945L10.0284 11L8.22284 12.8056C7.95435 13.074 7.95435 13.5093 8.22284 13.7778C8.49133 14.0463 8.92663 14.0463 9.19511 13.7778L11.0006 11.9723L12.8061 13.7778C13.0746 14.0463 13.5099 14.0463 13.7784 13.7778C14.0469 13.5093 14.0469 13.074 13.7784 12.8055L11.9729 11L13.7784 9.19451C14.0469 8.92603 14.0469 8.49073 13.7784 8.22224C13.5099 7.95376 13.0746 7.95376 12.8062 8.22224L11.0006 10.0278L9.19509 8.22222Z"
        fill=""
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.0007 1.14587C5.55835 1.14587 1.14648 5.55773 1.14648 11C1.14648 16.4423 5.55835 20.8542 11.0007 20.8542C16.443 20.8542 20.8548 16.4423 20.8548 11C20.8548 5.55773 16.443 1.14587 11.0007 1.14587ZM2.52148 11C2.52148 6.31713 6.31774 2.52087 11.0007 2.52087C15.6836 2.52087 19.4798 6.31713 19.4798 11C19.4798 15.683 15.6836 19.4792 11.0007 19.4792C6.31774 19.4792 2.52148 15.683 2.52148 11Z"
        fill=""
      />
    </svg>
  );
}
